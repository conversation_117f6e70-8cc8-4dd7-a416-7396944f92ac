<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>mermantic - Create and Share Mermaid Diagrams</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <header>
    <div class="container">
      <h1>mermantic</h1>
      <nav>
        <ul>
          <li><a href="/" class="active">Home</a></li>
          <li><a href="/login.html" id="login-link">Login</a></li>
          <li><a href="/register.html" id="register-link">Register</a></li>
          <li><a href="/dashboard.html" id="dashboard-link" style="display: none;">Dashboard</a></li>
          <li><a href="#" id="logout-link" style="display: none;">Logout</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="hero">
      <h2>Create, Edit, and Share Mermaid Diagrams</h2>
      <p>A simple web application for creating and sharing diagrams using Mermaid syntax.</p>
      <div class="cta-buttons">
        <a href="/register.html" class="nui-button primary">Get Started</a>
        <a href="#demo" class="nui-button secondary">See Demo</a>
      </div>
    </section>

    <section id="demo" class="demo-section">
      <h3>Try it out</h3>
      <div class="editor-container">
        <div class="editor-panel">
          <h4>Mermaid Syntax</h4>
          <textarea id="demo-editor" class="mermaid-editor">graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B</textarea>
        </div>
        <div class="preview-panel">
          <h4>Preview</h4>
          <div id="demo-preview" class="mermaid-preview"></div>
        </div>
      </div>
    </section>

    <section class="features">
      <h3>Features</h3>
      <div class="feature-grid">
        <div class="feature-card">
          <h4>Live Preview</h4>
          <p>See your diagrams render in real-time as you type.</p>
        </div>
        <div class="feature-card">
          <h4>Save & Share</h4>
          <p>Save your diagrams and share them with unique URLs.</p>
        </div>
        <div class="feature-card">
          <h4>User Dashboard</h4>
          <p>Manage all your diagrams in one place.</p>
        </div>
        <div class="feature-card">
          <h4>Multiple Diagram Types</h4>
          <p>Create flowcharts, sequence diagrams, class diagrams, and more.</p>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2023 mermantic. All rights reserved.</p>
    </div>
  </footer>

  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    // Initialize Mermaid with better error handling
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'default',
      logLevel: 'error', // Change to 'error' to hide warnings
      fontFamily: 'monospace',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      },
      er: {
        useMaxWidth: true
      },
      sequence: {
        useMaxWidth: true,
        wrap: true,
        showSequenceNumbers: false
      },
      gantt: {
        useMaxWidth: true
      },
      journey: {
        useMaxWidth: true
      },
      // Suppress errors in the console
      suppressErrors: true
    });

    // Demo editor functionality
    document.addEventListener('DOMContentLoaded', function() {
      const demoEditor = document.getElementById('demo-editor');
      const demoPreview = document.getElementById('demo-preview');

      function updatePreview() {
        try {
          // Clear the preview div
          demoPreview.innerHTML = '';

          // Generate a unique ID for the diagram
          const id = 'mermaid-diagram-' + Date.now();

          // Create a div element with the unique ID
          const mermaidDiv = document.createElement('div');
          mermaidDiv.id = id;
          mermaidDiv.className = 'mermaid';

          // Set the diagram code BEFORE appending to DOM
          mermaidDiv.textContent = demoEditor.value;

          // Append the div element to the preview div
          demoPreview.appendChild(mermaidDiv);

          // Add error handling callback
          mermaid.parseError = function(err, hash) {
            console.error('Mermaid parse error:', err);
            demoPreview.innerHTML = `<div class="error-message">
              <strong>Mermaid Syntax Error:</strong><br>
              ${err}<br><br>
              <strong>Common issues to check:</strong><br>
              - Indentation and whitespace<br>
              - Special characters (try using simple ASCII characters)<br>
              - Missing connections or closing brackets<br>
              - Subgraph syntax (ensure proper nesting)<br>
            </div>`;
          };

          // Use setTimeout to ensure the DOM is fully updated before rendering
          setTimeout(() => {
            // Render the diagram using the new run API
            mermaid.run({
              nodes: [document.getElementById(id)],
              suppressErrors: true
            }).then(() => {
              // Success - diagram rendered
            }).catch(error => {
              console.error('Error rendering Mermaid diagram:', error);
              demoPreview.innerHTML = `<div class="error-message">
                <strong>Error rendering diagram:</strong><br>
                ${error.message || 'Unknown error'}<br><br>
                <strong>Common issues to check:</strong><br>
                - Indentation and whitespace<br>
                - Special characters (try using simple ASCII characters)<br>
                - Missing connections or closing brackets<br>
                - Subgraph syntax (ensure proper nesting)<br>
              </div>`;
            });
          }, 0);
        } catch (error) {
          console.error('Error setting up Mermaid diagram:', error);
          demoPreview.innerHTML = `<div class="error-message">
            <strong>Error setting up diagram:</strong><br>
            ${error.message}<br><br>
            <strong>Common issues to check:</strong><br>
            - Indentation and whitespace<br>
            - Special characters (try using simple ASCII characters)<br>
            - Missing connections or closing brackets<br>
            - Subgraph syntax (ensure proper nesting)<br>
          </div>`;
        }
      }

      // Update preview on input with debounce
      let debounceTimer;
      demoEditor.addEventListener('input', function() {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(updatePreview, 300); // 300ms debounce
      });

      // Initial preview
      updatePreview();
    });
  </script>
</body>
</html>
