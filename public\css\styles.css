/* Main styles for mermantic */

/* Base styles */
:root {
  --primary-color: #4a6fa5;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --background-color: #ffffff;
  --text-color: #333333;
  --border-color: #dee2e6;
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Header */
header {
  background-color: var(--primary-color);
  color: white;
  padding: 1rem 0;
  box-shadow: var(--box-shadow);
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  margin: 0;
  font-size: 1.8rem;
}

header nav ul {
  display: flex;
  list-style: none;
}

header nav ul li {
  margin-left: 1.5rem;
}

header nav ul li a {
  color: white;
  text-decoration: none;
  font-weight: 500;
}

header nav ul li a:hover,
header nav ul li a.active {
  color: rgba(255, 255, 255, 0.8);
}

/* Main content */
main {
  padding: 2rem 0;
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: 1.5rem 0;
  margin-top: 2rem;
}

/* Hero section */
.hero {
  text-align: center;
  padding: 3rem 0;
}

.hero h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 2rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* Forms */
.auth-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-actions {
  margin-top: 2rem;
}

.auth-redirect {
  margin-top: 1.5rem;
  text-align: center;
}

/* Social login */
.social-login {
  margin-top: 2rem;
  text-align: center;
}

.social-login p {
  margin-bottom: 1rem;
  color: var(--secondary-color);
  position: relative;
}

.social-login p::before,
.social-login p::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.social-login p::before {
  left: 0;
}

.social-login p::after {
  right: 0;
}

.google-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: white;
  color: var(--dark-color);
  border: 1px solid var(--border-color);
  width: 100%;
  margin-bottom: 1rem;
  transition: background-color 0.3s, box-shadow 0.3s;
}

.google-btn:hover {
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.error-message {
  background-color: var(--danger-color);
  color: white;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: none;
}

/* Dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
}

.chart-card h4 {
  margin-bottom: 1rem;
}

.chart-preview-small {
  height: 150px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-preview-small svg {
  max-width: 100%;
  max-height: 150px;
  display: block;
  position: relative !important;
  z-index: 1;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

/* Editor */
.editor-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.editor-panel, .preview-panel {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: 1rem;
}

.mermaid-editor {
  width: 100%;
  height: 300px;
  font-family: monospace;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  resize: vertical;
}

.mermaid-preview {
  width: 100%;
  min-height: 300px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  background-color: white;
  overflow: auto;
}

.mermaid-preview .error-message {
  color: var(--danger-color);
  padding: 1rem;
  border: 1px solid var(--danger-color);
  border-radius: var(--border-radius);
  background-color: rgba(220, 53, 69, 0.1);
  font-family: system-ui, -apple-system, sans-serif;
  white-space: pre-wrap;
  margin-top: 0.5rem;
  line-height: 1.5;
  text-align: left;
}

.mermaid-preview .error-message strong {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.chart-preview-small .error-message {
  font-size: 0.8rem;
  padding: 0.5rem;
  overflow: auto;
  max-height: 140px;
}

/* Syntax indicator */
.syntax-indicator {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: normal;
  margin-left: 8px;
}

.syntax-indicator.valid {
  background-color: var(--success-color);
  color: white;
}

.syntax-indicator.invalid {
  background-color: var(--danger-color);
  color: white;
}

.syntax-indicator.checking {
  background-color: var(--secondary-color);
  color: white;
}

/* Help panel */
.help-toggle {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.help-toggle:hover {
  background: var(--primary-dark-color);
}

.help-panel {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.help-panel h5 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
}

.help-examples {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.help-example {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.help-example:hover {
  background: var(--primary-color);
  color: white;
}

.help-shortcuts {
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.help-shortcuts kbd {
  background: var(--dark-color);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 0.7rem;
}

/* Fullscreen editor */
.chart-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--background-color);
  z-index: 9999;
  padding: 1rem;
  overflow-y: auto;
}

.chart-editor.fullscreen .editor-container {
  height: calc(100vh - 200px);
}

.chart-editor.fullscreen .mermaid-editor {
  height: calc(100vh - 300px);
  min-height: 400px;
}

.chart-editor.fullscreen .mermaid-preview {
  height: calc(100vh - 300px);
  min-height: 400px;
}

/* Fullscreen modal */
.modal.fullscreen {
  background-color: var(--background-color);
}

.modal.fullscreen .modal-content {
  width: 100vw;
  height: 100vh;
  max-width: none;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

.modal.fullscreen .modal-body {
  flex: 1;
  max-height: none;
  padding: 2rem;
  overflow: hidden;
}

.modal.fullscreen .view-chart-container {
  height: 100%;
  min-height: auto;
  border: none;
  padding: 0;
  background: transparent;
}

.modal.fullscreen .modal-header {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
}

.modal.fullscreen .modal-footer {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
}

/* Zoom controls in fullscreen modal */
.modal.fullscreen .zoom-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Folder input container */
.folder-input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.folder-input-container input {
  flex: 1;
}

.folder-suggestions {
  min-width: 200px;
  max-width: 250px;
}

/* Chart cards with folder info */
.chart-card .chart-folder {
  font-size: 0.8rem;
  color: var(--secondary-color);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.chart-card .chart-folder:empty {
  display: none;
}

.chart-card .chart-notes {
  font-size: 0.8rem;
  color: var(--text-color);
  margin-top: 0.5rem;
  line-height: 1.4;
  max-height: 3.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.chart-card .chart-notes:empty {
  display: none;
}

/* Folder sections */
.folder-section {
  margin-bottom: 2rem;
}

.folder-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 2px solid var(--primary-color);
}

.folder-icon {
  margin-right: 0.5rem;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.folder-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.folder-count {
  margin-left: auto;
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Demo section */
.demo-section {
  margin: 3rem 0;
}

.features {
  margin: 3rem 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
}

/* Shared chart */
.shared-chart {
  max-width: 800px;
  margin: 0 auto;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: var(--background-color);
  margin: 5% auto;
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  width: 80%;
  max-width: 900px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
}

.modal-body {
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.close-modal {
  color: var(--secondary-color);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: var(--dark-color);
}

.view-chart-container {
  width: 100%;
  min-height: 400px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background-color: white;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-chart-container svg {
  max-width: 100%;
  margin: 0 auto;
  display: block;
  position: relative !important;
  z-index: 1;
}

/* Mermaid diagrams */
.mermaid {
  font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
}

/* Zoom controls */
.zoom-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  justify-content: flex-end;
  background-color: rgba(240, 240, 240, 0.9);
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ddd;
  position: sticky;
  top: 10px;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.zoom-controls button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin: 0 2px;
}

.zoom-controls button:hover {
  background-color: var(--primary-dark-color);
}

.zoom-controls button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

/* Zoomable container */
.zoomable-container {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.zoomable-content {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

/* Fix for Mermaid SVG positioning */
.mermaid-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Force SVG to stay within its container */
svg {
  position: relative !important;
  z-index: 1 !important;
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  overflow: visible !important;
}

/* Fix for absolute positioning in Mermaid diagrams */
.mermaid foreignObject,
.mermaid g,
.mermaid rect,
.mermaid circle,
.mermaid ellipse,
.mermaid polygon,
.mermaid path,
.mermaid line,
.mermaid text {
  position: relative !important;
}

/* More aggressive fix for Mermaid diagrams */
body svg,
.chart-preview-small svg,
.view-chart-container svg,
#chart-preview svg {
  position: static !important;
  z-index: 10 !important;
  max-width: 100% !important;
  max-height: 100% !important;
  overflow: visible !important;
}

/* Target specific chart previews */
[id^="chart-preview-"] {
  position: relative !important;
  z-index: 5 !important;
  overflow: hidden !important;
  max-height: 150px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure footer stays below content */
footer {
  position: relative;
  z-index: 1;
  margin-top: 2rem;
  clear: both;
}

/* Responsive */
@media (max-width: 768px) {
  .editor-container {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}
